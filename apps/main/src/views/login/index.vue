<template>
    <div class="login-container" :class="{ 'mobile-view': isMobileView }">
        <LoginBg />
        <div class="login-content">
            <div class="login-box">
                <div class="greet-title-container">
                    <!-- <h2 class="greet-title">欢迎登录</h2> -->
                    <div class="greet-title" :class="{ active: loginFormData.mode === 'sms' }" @click="onSwitchMode('sms')">验证码登录</div>
                    <div class="greet-title" :class="{ active: loginFormData.mode === 'password' }" @click="onSwitchMode('password')">账号密码登录</div>
                </div>
                <p class="greet-site">瞰荐职测考试系统</p>
                <b-form ref="loginFormRef" class="login-form" :model="loginFormData" :rules="rules" layout="vertical">
                    <b-form-item field="account" v-if="loginFormData.mode === 'password'" class="login-form-item" label="账号">
                        <b-input v-model="loginFormData.account" allowClear placeholder="请输入账号" />
                    </b-form-item>
                    <b-form-item field="phone" v-else class="login-form-item" label="手机号">
                        <b-input :modelValue="loginFormData.phone" @update:modelValue="onPhoneChange" allowClear placeholder="请输入手机号" :maxlength="11" />
                    </b-form-item>
                    <b-form-item field="password" v-if="loginFormData.mode === 'password'" class="login-form-item" label="密码">
                        <b-input-password v-model="loginFormData.password" allowClear placeholder="请输入密码" />
                    </b-form-item>
                    <b-form-item field="smsCode" v-else class="login-form-item" label="验证码">
                        <b-input v-model="loginFormData.smsCode" placeholder="请输入手机号验证码" label="验证码" :maxlength="6">
                            <template #suffix>
                                <ManMachineCore
                                    :ajaxFn="loginCaptchaSms"
                                    :extraReqParams="extraReqParams"
                                    successToast="发送成功！"
                                    :beforeSend="beforeSend"
                                    @success="onVerifySuccess"
                                />
                            </template>
                        </b-input>
                    </b-form-item>

                    <div class="agreement">
                        <b-checkbox v-model="agreePolicy">
                            <span>我已阅读并同意</span>
                            <a :href="USER_AGREEMENT_URL" target="_blank" @click="onReadAgreement">《用户协议》</a>
                            和
                            <a :href="PRIVACY_POLICY_URL" target="_blank" @click="onReadPrivacyPolicy">《隐私政策》</a>
                        </b-checkbox>
                        <p v-if="!agreePolicy && showAgreementTips" class="agreement-tips">
                            <IconExclamationCircleFill size="16" />
                            <span>请先同意用户协议及隐私政策</span>
                        </p>
                    </div>

                    <ManMachineCore
                        style="flex: 1"
                        v-if="loginFormData.mode === 'password'"
                        btnText="人机校验"
                        :extraReqParams="extraReqParams"
                        successToast="验证成功！"
                        @success="(data: any) => onVerifySuccess(data, 'manMachineVerify')"
                    >
                        <template #default="{ onClick, loading }">
                            <b-button style="border-radius: 8px" block type="primary" :loading="isLoading || loading" size="xlarge" @click.prevent="onClick">
                                {{ btnText }}
                            </b-button>
                        </template>
                    </ManMachineCore>

                    <b-button v-else style="border-radius: 8px" block type="primary" :loading="isLoading" size="xlarge" @click.prevent="submit">
                        {{ btnText }}
                    </b-button>
                    <div class="help-text">如需更多帮助，请联系{{ HELP_PHONE }}</div>
                </b-form>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import ManMachineCore from '@/components/man-machine-core/index.vue';
import { IconExclamationCircleFill } from '@boss/design/es/icon';
import { HELP_PHONE, PRIVACY_POLICY_URL, USER_AGREEMENT_URL } from '@crm/exam-constants';
import { useIsMobile } from '@crm/exam-hooks';
import { ref, computed, watch } from 'vue';
import { useLoginType } from './composables/use-login-type';
import LoginBg from './login-bg.vue';
import { useLogin } from './composables/use-login';
import { useTrack } from './composables/use-track';
import { checkPhone } from './util';

defineOptions({
    name: 'LoginIndex',
});

const { loginType, setLoginType } = useLoginType();

const loginFormData = ref<{
    mode: 'sms' | 'password';
    account: string;
    phone: string;
    password: string;
    smsCode: string;
    manMachineVerify: boolean;
}>({
    mode: loginType.value,
    account: '',
    phone: '',
    password: '',
    smsCode: '',
    manMachineVerify: false,
});

// 监听 loginType 变化，保持 loginFormData.mode 同步
watch(loginType, (val) => {
    loginFormData.value.mode = val;
});

const rules = {
    phone: [
        { required: true, message: '请输入手机号' },
        {
            validator: (value: string, callback: any) => {
                const checkPhoneValid = checkPhone(value);
                if (!checkPhoneValid.valid) {
                    callback(checkPhoneValid.msg);
                }
            },
        },
    ],
    password: [{ required: true, message: '请输入密码' }],
    smsCode: [{ required: true, message: '请输入手机号验证码' }],
    account: [{ required: true, message: '请输入账号' }],
};

const loginFormRef = ref<InstanceType<any>>();

const onPhoneChange = (value: string) => {
    const numericonly = value.replace(/[^\d]/g, '');
    loginFormData.value.phone = numericonly.slice(0, 11);
};

// 登录相关
const { isLoading, btnText, encryptExamId, login, executeGetSmsCodeFlow } = useLogin();

// 埋点相关
const { trackLogin, trackLoginSuccess, trackLoginFail, trackAgreement, trackPrivacyPolicy } = useTrack();

const manMachineVerifyData = ref();
const onVerifySuccess = (data: any, type?: 'manMachineVerify' | 'smsCaptcha') => {
    manMachineVerifyData.value = data;
    if (type === 'manMachineVerify') {
        submit();
    }
};
// 协议相关
const agreePolicy = ref(false);
const showAgreementTips = ref(false);

// 监听是否是移动端
const { isMobileView } = useIsMobile();

// 登录方式切换时，调用 setLoginType
function onSwitchMode(mode: 'sms' | 'password') {
    setLoginType(mode);
}

// 发送验证码 - 使用可追踪流程
function loginCaptchaSms(params: any, options: any) {
    return executeGetSmsCodeFlow(params, options);
}

const extraReqParams = computed(() => ({
    mobile: loginFormData.value.phone,
}));

function beforeSend() {
    const checkPhoneValid = checkPhone(loginFormData.value.phone);
    if (!checkPhoneValid.valid) {
        Toast.danger({ content: checkPhoneValid.msg });
    }
    return checkPhoneValid.valid;
}

async function submit() {
    const res = await loginFormRef.value.validate();
    if (res) return;
    trackLogin();

    if (!manMachineVerifyData.value) {
        Toast.danger({ content: '请点击完成验证' });
        return;
    }

    if (isLoading.value) {
        return;
    }

    if (!agreePolicy.value) {
        showAgreementTips.value = true;
        return;
    }

    const params =
        loginFormData.value.mode === 'password'
            ? {
                  account: loginFormData.value.account,
                  password: loginFormData.value.password,
                  loginType: 2,
                  encryptExamId,
              }
            : {
                  mobile: loginFormData.value.phone,
                  smsCaptcha: loginFormData.value.smsCode,
                  loginType: 1,
                  encryptExamId,
              };

    const { success, message } = await login(params, { headers: manMachineVerifyData.value });

    if (success) {
        trackLoginSuccess(loginFormData.value.phone);
        setLoginType(loginFormData.value.mode); // 登录成功时保存
    } else {
        trackLoginFail(loginFormData.value.phone, message || '登录失败');
    }
}

function onReadAgreement() {
    trackAgreement();
}

function onReadPrivacyPolicy() {
    trackPrivacyPolicy();
}
</script>

<style lang="less" scoped>
.greet-title-container {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 16px;
    height: 45px;
}

.greet-title {
    color: #5d7080;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.4; /* 28px */
    cursor: pointer;
    transition: all 0.2s;

    &.active {
        color: #00a6a7;
        font-family: 'PingFang SC';
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 1.4; /* 34px */
        text-align: center;

        &::after {
            background: #00a6a7;
            border-radius: 2px;
        }
    }
    &::after {
        content: '';
        display: block;
        width: 24px;
        height: 3px;
        margin: 0 auto;
        margin-top: 3px;
    }

    // font-size: 28px;
    // font-weight: 600;
    // color: #29292d;
    // line-height: 42px;
}

:deep(.login-form-item) {
    .b-input-wrapper {
        height: 48px;
        // border: 1px solid #d3d8e6;
        border-radius: 8px;
    }
    .b-form-item-label-col {
        color: #5d7080 !important;
        padding-bottom: 0;
        margin-bottom: 0;
    }
    .b-form-item-label-required-symbol {
        display: none;
    }
}
</style>
<style lang="less" scoped>
.login-container {
    overflow-y: auto;
    display: flex;
    height: 100%;
    min-width: 1080px;
    min-height: 520px;

    user-select: none;

    /* 防止移动端缩放和点击效果 */
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;

    :deep(.kanjian-login) {
        max-width: 1080px;
        min-width: 400px;
        width: 45%;
    }

    .login-content {
        position: relative;
        flex: 1 0 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-width: 500px;
        background-color: #fff;

        .login-warning-tip {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            padding: 12px 12px 12px 40px;
            line-height: 20px;
            background-color: #ffebba;
            z-index: 2;

            svg {
                position: absolute;
                top: 14px;
                left: 16px;
            }

            a {
                text-decoration: underline;
            }
        }

        .login-box {
            width: 65%;
            max-width: 520px;
        }

        .greet-site {
            font-size: 14px;
            color: #939cbc;
            line-height: 22px;
        }

        .btn-sms {
            position: absolute;
            right: 12px;
            height: 24px;
            padding: 0 8px;
            border-radius: 12px;
            line-height: 24px;
            color: #12ada9;
            font-size: 14px;
            cursor: pointer;

            &.normal {
                &:hover {
                    background: rgba(20, 199, 194, 0.1);
                }
            }

            &.counting {
                color: #a5a3ba;
                cursor: initial;

                &::before {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: -8px;
                    width: 1px;
                    height: 24px;
                    margin-top: -12px;
                    background: #f0f1f2;
                }
            }
        }

        .login-form {
            margin-top: 32px;
            margin-bottom: 8px;

            .form-item {
                margin-bottom: 32px;
                height: 48px;
            }

            .b-button {
                height: 48px;
                font-size: 18px;
                font-weight: 500;
            }

            .help-text {
                padding: 12px 0;
                line-height: 17px;
                font-size: 12px;
                color: #939cbc;
                text-align: center;
            }
        }

        .agreement {
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            color: #939cbc;
            line-height: 22px;

            .ui-checkbox {
                margin-right: 4px;
            }

            .agreement-tips {
                position: absolute;
                bottom: -20px;
                left: 0;
                color: #f77233;
                font-size: 12px;

                svg {
                    margin-right: 6px;
                }

                & > * {
                    vertical-align: middle;
                }
            }

            a:hover {
                color: #0f9490;
            }
        }
    }

    // 移动端样式
    &.mobile-view {
        // min-width: auto;
        min-width: 355px;
        min-height: auto;

        :deep(.kanjian-login) {
            display: none;
        }

        .login-content {
            // margin: 0 auto;
            min-width: auto;
            padding: 20px;
            align-items: flex-start;
            padding-top: 100px;

            .login-box {
                max-width: 450px;
                width: 100%;
                // max-width: 100%;
            }

            .greet-title {
                font-size: 24px;
                // text-align: center;
                line-height: 36px;
            }

            .greet-site {
                // text-align: center;
            }

            .login-form {
                margin-top: 24px;

                .form-item {
                    margin-bottom: 24px;
                }

                .b-button {
                    height: 44px;
                    font-size: 16px;
                }
            }

            .agreement {
                margin-bottom: 24px;
                font-size: 12px;
                flex-wrap: wrap;
                justify-content: center;

                .agreement-tips {
                    text-align: center;
                    width: 100%;
                }
            }
        }
    }
}
</style>
