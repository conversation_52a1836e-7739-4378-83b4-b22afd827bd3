<template>
    <!-- 目前考试地址错误，仅一个场景进入下面的逻辑，即无参数访问跟路径https://kaoshi-qa.weizhipin.com/ -->
    <b-pagetip v-if="!seqId" status="empty">
        <template #empty-content> 考试地址错误，请重新从邮件中获取地址访问 </template>
    </b-pagetip>

    <!-- 正常考试逻辑 -->
    <template v-if="examIdCheckResult === 'Valid'">
        <!-- 确保考试信息加载完全，再去渲染业务路由和组件 -->
        <PageHeader v-if="!isMobileView" />
        <RouterView />
    </template>
</template>

<script lang="ts" setup name="PageLayout">
import { useRouter } from 'vue-router';
import PageHeader from './components/page-header.vue';
import { useMonitorStore } from '@/store/use-monitor-store';
import { useIsMobile } from '@crm/exam-hooks';
import { jumpLogin } from '@/utils/jump-url';
import { useRouteParamId } from '../monitor/hooks/useRouteParamId';
import { formatDate } from '@crm/exam-utils';
import { Watermark } from '@boss/design';
import { timeCenter } from '@/store/time';

// 类型定义
export type TExamIdCheckResult = 'NotChecked' | 'Valid' | 'Invalid';

// 应用防抖逻辑
const { currentTime } = timeCenter;

const { isMobileView } = useIsMobile();

const monitorStore = useMonitorStore();

const examIdCheckResult = ref<TExamIdCheckResult>('NotChecked');
const $router = useRouter();
const { examId, seqId } = useRouteParamId();

async function getBaseInfo() {
    try {
        const res = await monitorStore.fetchBaseInfo({
            seqId,
            examId,
        });
        // res.code === 108小场次考试不存在， 踢出登录
        if (res.code === 0 || res.code === 108) {
            if (res.code === 108 && !examId) {
                examIdCheckResult.value = 'Invalid';
                // 踢出登录
                jumpLogin(seqId);
                return;
            }

            const { examInfo, userName, examineeId } = res.data || {};

            (Watermark as any).init({
                opacity: 0.1,
                content: `${userName}-${examineeId}-${formatDate(currentTime.value, 'yyyy-MM-dd')}`,
            });

            let url = `/exam-list/${seqId}`;

            // 如果小场次 id 存在 并且可以获取到试卷信息 并且试卷状态为可作答 并且没有提交试卷
            if (examId && examInfo && examInfo.status === 2 && !examInfo.hasCommitPaper) {
                url = `/monitor?seqId=${seqId}&examId=${examId}`;
            }

            $router.replace(url);

            examIdCheckResult.value = 'Valid';
        } else {
            examIdCheckResult.value = 'Invalid';
            // 踢出登录
            jumpLogin(seqId);
        }
    } catch (error) {
        examIdCheckResult.value = 'Invalid';
        // 踢出登录
        jumpLogin(seqId);
    }
}

onMounted(() => {
    getBaseInfo();
});
</script>
