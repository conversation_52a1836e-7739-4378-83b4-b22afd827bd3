// services/tracker.service.ts
import { v4 as uuidv4 } from 'uuid'; // Example ID generation
import { logger, Logger, LogLevel, LogR<PERSON>ord, TrackerEventData, originalConsole } from '@/utils/logger';

class TrackerService {
    // private logBuffer: LogEntry[] = []; // REMOVED: logBuffer is no longer used
    private batchSize = 10; // How many tracker events to send in one batch
    private flushTimer: number | null = null;
    private isFlushing = false; // Guard against concurrent flushes
    private flushInterval = 5000; // 添加 flushInterval 属性，默认 5 秒

    constructor() {
        // Ensure global logger is available
        if (!logger) {
            originalConsole.error('[TrackerService] Global logger not initialized!');
        }
    }

    generateFlowId(): string {
        return uuidv4();
    }

    // currentFlowId and related context methods are less relevant now as flowId is explicitly passed
    // and managed by useTrackableFlow, and then Logger instances.

    private sanitizePayload(data: any): any {
        if (!data) return data;
        // VERY IMPORTANT: Implement robust sanitization logic here!
        // Remove/mask passwords, tokens, PII, etc.
        // Example: Replace 'password' field
        const sensitiveKeys = ['password', 'token', 'secret', 'wsConnectSecret', 'idCard'];
        let sanitizedData = JSON.parse(JSON.stringify(data)); // Deep clone

        function traverseAndSanitize(obj: any) {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    if (sensitiveKeys.includes(key)) {
                        obj[key] = '***REDACTED***';
                    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                        traverseAndSanitize(obj[key]);
                    }
                }
            }
        }

        traverseAndSanitize(sanitizedData);
        return sanitizedData;
    }

    private enqueueTrackerEvent(
        flowId: string | null, // flowId can be null for steps logged outside a formal flow
        type: TrackerEventData['type'],
        name: string,
        details: Partial<Omit<TrackerEventData, 'type' | 'name'>> = {},
    ): void {
        const eventData: TrackerEventData = {
            type,
            name,
            ...(details.status && { status: details.status }),
            ...(details.durationMs !== undefined && { durationMs: details.durationMs }),
            ...(details.payload && { payload: this.sanitizePayload(details.payload) }),
            ...(details.error && { error: String(details.error).substring(0, 500) }),
        };

        // Use a logger instance that is aware of the flowId
        const eventLogger = flowId ? logger.withFlowId(flowId) : logger;
        eventLogger.track(type === 'flowStart' || type === 'flowEnd' ? LogLevel.INFO : LogLevel.DEBUG, eventData); // LogLevel.INFO is a common default for tracking

        // Trigger flush check (non-blocking)
        this.scheduleFlush();
    }

    private scheduleFlush(): void {
        if (!this.flushTimer && !this.isFlushing) {
            this.flushTimer = window.setTimeout(() => {
                this.flushTimer = null; // Clear timer before flush starts
                this.flushLogs().catch((err) => {
                    originalConsole.error('[TrackerService] Error during scheduled flushLogs:', err);
                });
            }, this.flushInterval);
        }
    }

    async flushLogs(): Promise<void> {
        if (this.isFlushing) {
            logger.debug('[TrackerService] Flush already in progress.');
            return;
        }
        if (this.flushTimer) {
            // If a scheduled flush is pending, clear it as we are flushing now.
            clearTimeout(this.flushTimer);
            this.flushTimer = null;
        }

        this.isFlushing = true;

        try {
            const loggerConfig = Logger.getDefaultConfig(); // Get config from Logger
            const allLogs = await Logger.getPersistenceCache(loggerConfig.persistenceMaxSize);
            const trackerLogsToSend: LogRecord[] = [];
            for (const record of allLogs) {
                if (record.logKind === 'tracker' && !record.synced) {
                    trackerLogsToSend.push(record);
                    if (trackerLogsToSend.length >= this.batchSize) {
                        break; // Gather up to batchSize logs
                    }
                }
            }

            if (trackerLogsToSend.length === 0) {
                logger.debug('[TrackerService] No tracker logs to send.');
                this.isFlushing = false;
                return;
            }

            // Transform LogRecord[] to the format expected by the backend
            // This usually involves taking record.trackerEvent and common fields like flowId, sessionId, timestamp
            const backendPayload = trackerLogsToSend.map((record) => ({
                ...(record.trackerEvent || {}),
                // Ensure essential top-level LogRecord fields are included if backend expects them
                flowId: record.flowId,
                sessionId: record.sessionId,
                timestamp: record.timestamp, // LogRecord timestamp
                recordId: record.recordId, // Include recordId for potential backend correlation
                // Add other fields from record.globalContext if needed by backend
                // userId: record.globalContext?.userId,
            }));

            logger.debug('[TrackerService] Sending tracker logs:', backendPayload);

            // 修改：尝试发送到后端，无论结果如何都标记为已同步
            await this.sendToBackend(backendPayload);

            // 只要尝试发送了，就标记为已同步（无论是否成功）
            const sentRecordIds = trackerLogsToSend.map((r) => r.recordId);
            await Logger.updateLogSyncStatus(sentRecordIds, true);
            logger.debug(`[TrackerService] Attempted to send ${sentRecordIds.length} tracker logs and marked as synced.`);
        } catch (error) {
            originalConsole.error('[TrackerService] Failed to flush tracker logs:', error);
            // 即使出现异常，我们也应该标记为已同步，因为我们已经尝试过了
            // 但为了安全起见，这里保持原有的错误处理逻辑
        } finally {
            this.isFlushing = false;
            // If there might be more logs, schedule another flush check,
            // especially if the batch was full or an error occurred.
            // However, the interval timer will naturally re-trigger if not cleared.
            // For simplicity, let existing interval logic handle next flush.
            // Consider calling scheduleFlush() here if aggressive retries are needed.
        }
    }

    private async sendToBackend(payload: any[]): Promise<void> {
        try {
            // 使用 apmSendCustom 进行上报，无论结果如何都认为已尝试上报
            const response = await apmSendCustom(payload);
            logger.debug('[TrackerService] APM upload attempt completed:', response);
        } catch (error) {
            // 即使发生错误，也认为已经尝试过上报了
            originalConsole.warn('[TrackerService] Error during APM upload attempt (marking as attempted):', error);
        }
    }

    // --- Public Logging Methods --- Wrapper around enqueueTrackerEvent
    logFlowStart(flowId: string, flowName: string, initialPayload?: any): void {
        this.enqueueTrackerEvent(flowId, 'flowStart', flowName, { payload: initialPayload });
    }

    logFlowEnd(flowId: string, flowName: string, durationMs: number, status: 'success' | 'failure', resultOrError?: any): void {
        const details: Partial<Omit<TrackerEventData, 'type' | 'name'>> = { durationMs, status };
        if (status === 'success') {
            details.payload = resultOrError;
        } else {
            details.error = resultOrError instanceof Error ? resultOrError.message : String(resultOrError);
        }
        this.enqueueTrackerEvent(flowId, 'flowEnd', flowName, details);
    }

    logStep(flowId: string | null, stepName: string, payload?: any): void {
        this.enqueueTrackerEvent(flowId, 'step', stepName, { payload });
    }
}

export const trackerService = new TrackerService(); // Singleton instance

// const defaultConfig = { persistenceMaxSize: 1000 }; // REMOVED Placeholder
