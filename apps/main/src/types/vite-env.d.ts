/// <reference types="vite/client" />
/// <reference path="./nebulartc.d.ts" />

// Vue文件类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 扩展Vite的环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_DEPLOY_ENV?: string;
  // 在这里添加你的环境变量类型定义
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
