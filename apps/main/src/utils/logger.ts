/* eslint-disable no-console */
import { v4 as uuidv4 } from 'uuid'; // Using uuid for session ID generation

// --- Interfaces and Enums ---

// 定义日志级别
export enum LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5, // Typically treated like ERROR but indicates a critical failure
    OFF = 99, // Special level to disable logging completely
}

// 定义追踪事件的具体数据结构
export interface TrackerEventData {
    type: 'flowStart' | 'flowEnd' | 'step'; // 事件类型：流程开始、流程结束、步骤
    name: string; // 流程名称或步骤名称
    status?: 'success' | 'failure'; // 仅用于 flowEnd 事件
    durationMs?: number; // 仅用于 flowEnd 事件，流程执行耗时（毫秒）
    payload?: any; // 事件相关的负载数据，会被清理
    error?: string; // 错误信息，通常在 status 为 'failure' 时提供
}

// 定义 logger 使用的 console 方法名
type ConsoleMethodName = 'debug' | 'info' | 'warn' | 'error' | 'log';

// 定义 LogLevelInfo 的结构
interface LevelInfo {
    name: string;
    color: string;
    method: ConsoleMethodName;
}

// 日志级别对应的显示名称和颜色
const LogLevelInfo: Record<LogLevel.TRACE | LogLevel.DEBUG | LogLevel.INFO | LogLevel.WARN | LogLevel.ERROR | LogLevel.FATAL, LevelInfo> = {
    [LogLevel.TRACE]: { name: 'TRACE', color: '#A0A0A0', method: 'debug' }, // Use console.debug for TRACE
    [LogLevel.DEBUG]: { name: 'DEBUG', color: '#909399', method: 'debug' },
    [LogLevel.INFO]: { name: 'INFO', color: '#409EFF', method: 'log' }, // 使用 console.log
    [LogLevel.WARN]: { name: 'WARN', color: '#E6A23C', method: 'log' }, // 使用 console.log，用颜色区别
    [LogLevel.ERROR]: { name: 'ERROR', color: '#F56C6C', method: 'log' }, // 使用 console.log，用颜色区别
    [LogLevel.FATAL]: { name: 'FATAL', color: '#FF0000', method: 'warn' }, // 使用 console.warn
};

// 单条日志记录的结构
export interface LogRecord {
    timestamp: string; // ISO 8601 format UTC
    level: LogLevel;
    levelName: string;
    message: string; // Primary message string
    args: any[]; // Additional arguments/data (for generic logs)
    context?: string | undefined; // Logger instance context (e.g., component name)
    sessionId?: string | undefined; // Unique ID for the user session
    globalContext?: Record<string, any> | undefined; // Extra global data (userId, examId etc.)
    error?:
        | {
              // Optional structured error info (for generic logs)
              name: string;
              message: string;
              stack?: string;
          }
        | undefined;
    flowId?: string | undefined; // 关联到业务流程ID
    recordId: string; // 每条日志的唯一ID，便于精确操作
    synced: boolean; // 标记是否已同步到远程

    logKind: 'generic' | 'tracker'; // 新增：日志种类，区分普通日志和追踪事件
    trackerEvent?: TrackerEventData | undefined; // 新增：追踪事件的专属数据，当 logKind 为 'tracker' 时填充
}

// Logger 配置接口
export interface LoggerConfig {
    level: LogLevel; // Minimum level to log for this context/globally
    consoleOutput: boolean; // Output to browser console?
    consoleJson: boolean; // Output JSON to console instead of formatted text?
    useLocalStorageCache: boolean; // Use simple localStorage for basic caching (dev only?)
    enablePersistence: boolean; // Enable persistent buffering (IndexedDB)
    persistenceDbName: string; // IndexedDB database name
    persistenceStoreName: string; // IndexedDB object store name
    persistenceMaxSize: number; // Max number of records to keep in persistence
    sessionId?: string; // Manually set Session ID (otherwise auto-generated)
    globalContext: Record<string, any>; // Static global context (userId, examId)
    maxCacheSize: number; // Max entries for simple localStorage cache
}

// --- Utilities ---

// 保存原始 console 方法
export const originalConsole = {
    log: console.log.bind(console),
    warn: console.warn.bind(console),
    error: console.error.bind(console),
    info: console.info.bind(console),
    debug: console.debug.bind(console),
    trace: console.trace ? console.trace.bind(console) : console.debug.bind(console),
    group: console.group.bind(console),
    groupCollapsed: console.groupCollapsed.bind(console),
    groupEnd: console.groupEnd.bind(console),
    time: console.time.bind(console),
    timeEnd: console.timeEnd.bind(console),
};

// 深度克隆对象并移除不可序列化的内容
function sanitizeForStorage(obj: any, seen = new WeakMap()): any {
    // 处理基本类型
    if (obj === null || obj === undefined) return obj;
    if (typeof obj !== 'object' && typeof obj !== 'function') return obj;

    // 处理函数
    if (typeof obj === 'function') return '[Function]';

    // 处理日期
    if (obj instanceof Date) return new Date(obj);

    // 处理正则表达式
    if (obj instanceof RegExp) return obj.toString();

    // 处理错误对象
    if (obj instanceof Error) {
        return {
            name: obj.name,
            message: obj.message,
            stack: obj.stack,
        };
    }

    // 检查循环引用
    if (seen.has(obj)) return '[Circular]';
    seen.set(obj, true);

    // 处理数组
    if (Array.isArray(obj)) {
        return obj.map((item) => sanitizeForStorage(item, seen));
    }

    // 处理普通对象
    const result: Record<string, any> = {};
    for (const [key, value] of Object.entries(obj)) {
        try {
            // 尝试序列化，如果失败则使用替代值
            result[key] = sanitizeForStorage(value, seen);
        } catch (e) {
            result[key] = `[Unserializable: ${typeof value}]`;
        }
    }

    return result;
}

// 格式化时间戳 (简单版，可按需调整)
function formatSimpleTimestamp(): string {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
    return `${hours}:${minutes}:${seconds}.${milliseconds}`;
}

// Helper to extract Error info safely
function formatError(error: any): LogRecord['error'] | undefined {
    if (error instanceof Error) {
        return {
            name: error.name,
            message: error.message,
            stack: error.stack || '',
        };
    }
    return undefined;
}

// --- Core Logic & State ---

const defaultConfig: LoggerConfig = {
    level: import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.INFO, // Default to DEBUG in dev, INFO in prod
    consoleOutput: true,
    consoleJson: false,
    useLocalStorageCache: false, // Avoid using for primary logging in prod
    enablePersistence: true, // Enable robust buffering by default
    persistenceDbName: 'loggerPersistenceDB',
    persistenceStoreName: 'logQueue',
    persistenceMaxSize: 1000, // Store max 1000 records
    sessionId: uuidv4(), // Auto-generate session ID
    globalContext: {},
    maxCacheSize: 200, // Limit localStorage cache if used
};

const contextConfigs = new Map<string, Partial<LoggerConfig>>();
const timers = new Map<string, number>();

// --- Middleware ---
export type Middleware = (record: LogRecord) => LogRecord | false | null | Promise<LogRecord | false | null>; // Return false/null to skip logging, or Promise for async middleware
const middlewares: Middleware[] = [];
let isProcessingMiddleware = false; // 添加中间件处理标记

// --- 异步队列处理系统 ---
// 用于存储需要异步处理的日志记录
const asyncProcessingQueue: LogRecord[] = [];
let isProcessingQueue = false;

// 异步处理队列中的日志记录
async function processAsyncQueue() {
    if (isProcessingQueue || asyncProcessingQueue.length === 0) return;

    isProcessingQueue = true;
    try {
        // 复制当前队列并清空原队列
        const records = [...asyncProcessingQueue];
        asyncProcessingQueue.length = 0;

        // 处理持久化存储
        if (defaultConfig.enablePersistence) {
            // 在保存前清理记录，确保可序列化
            const sanitizedRecords = records.map((record) => {
                // 创建一个干净的记录副本
                const sanitizedRecord = { ...record };

                // 清理可能包含不可序列化内容的字段
                if (sanitizedRecord.args && sanitizedRecord.args.length > 0) {
                    sanitizedRecord.args = sanitizedRecord.args.map((arg) => sanitizeForStorage(arg));
                }

                // 确保 globalContext 也被清理
                if (sanitizedRecord.globalContext) {
                    sanitizedRecord.globalContext = sanitizeForStorage(sanitizedRecord.globalContext);
                }

                return sanitizedRecord;
            });

            await saveToPersistence(sanitizedRecords);
        }

        // 处理 localStorage 缓存
        if (defaultConfig.useLocalStorageCache) {
            try {
                const key = 'LOGGER_CACHE';
                let cache: LogRecord[] = JSON.parse(localStorage.getItem(key) || '[]');

                // 同样清理记录用于 localStorage
                const sanitizedRecords = records.map((record) => {
                    const sanitizedRecord = { ...record };
                    if (sanitizedRecord.args && sanitizedRecord.args.length > 0) {
                        sanitizedRecord.args = sanitizedRecord.args.map((arg) => sanitizeForStorage(arg));
                    }
                    if (sanitizedRecord.globalContext) {
                        sanitizedRecord.globalContext = sanitizeForStorage(sanitizedRecord.globalContext);
                    }
                    return sanitizedRecord;
                });

                cache.push(...sanitizedRecords);
                if (cache.length > defaultConfig.maxCacheSize) {
                    cache = cache.slice(cache.length - defaultConfig.maxCacheSize); // Keep latest
                }
                localStorage.setItem(key, JSON.stringify(cache));
            } catch (e) {
                originalConsole.warn('Logger: Failed to write to localStorage cache.', e);
            }
        }
    } catch (error) {
        originalConsole.error('Logger: Failed to process async queue:', error);
    } finally {
        isProcessingQueue = false;

        // 如果队列中还有项目，继续处理
        if (asyncProcessingQueue.length > 0) {
            setTimeout(processAsyncQueue, 0);
        }
    }
}

// 添加记录到异步处理队列
function addToAsyncQueue(record: LogRecord) {
    asyncProcessingQueue.push(record);
    // 启动异步处理
    if (!isProcessingQueue) {
        setTimeout(processAsyncQueue, 0);
    }
}

// --- Persistence (IndexedDB) ---
let dbPromise: Promise<IDBDatabase | null> | null = null;

function getDb(): Promise<IDBDatabase | null> {
    if (!defaultConfig.enablePersistence || typeof indexedDB === 'undefined') {
        return Promise.resolve(null);
    }
    if (!dbPromise) {
        dbPromise = new Promise((resolve, _reject) => {
            const request = indexedDB.open(defaultConfig.persistenceDbName, 1);
            request.onupgradeneeded = (event) => {
                const db = (event.target as IDBOpenDBRequest).result;
                if (!db.objectStoreNames.contains(defaultConfig.persistenceStoreName)) {
                    db.createObjectStore(defaultConfig.persistenceStoreName, { autoIncrement: true });
                }
            };
            request.onsuccess = (event) => resolve((event.target as IDBOpenDBRequest).result);
            request.onerror = (event) => {
                originalConsole.error('Logger Persistence DB Error:', (event.target as IDBOpenDBRequest).error);
                resolve(null); // Resolve with null on error to avoid blocking
            };
        });
    }
    return dbPromise;
}

async function saveToPersistence(records: LogRecord[]): Promise<void> {
    const db = await getDb();
    if (!db || !records.length) return;

    try {
        const tx = db.transaction(defaultConfig.persistenceStoreName, 'readwrite');
        const store = tx.objectStore(defaultConfig.persistenceStoreName);
        let count = await new Promise<number>((resolve, reject) => {
            const req = store.count();
            req.onsuccess = () => resolve(req.result);
            req.onerror = reject;
        });

        // Prune old records if exceeding max size
        if (count + records.length > defaultConfig.persistenceMaxSize) {
            const keysToDelete = await new Promise<IDBValidKey[]>((resolve, reject) => {
                const req = store.getAllKeys();
                req.onsuccess = () => {
                    const keys = req.result;
                    const numToDelete = count + records.length - defaultConfig.persistenceMaxSize;
                    resolve(keys.slice(0, numToDelete));
                };
                req.onerror = reject;
            });
            for (const key of keysToDelete) {
                await new Promise<void>((resolve, reject) => {
                    const req = store.delete(key);
                    req.onsuccess = () => resolve();
                    req.onerror = reject;
                });
            }
        }

        // Add new records
        for (const record of records) {
            await new Promise<void>((resolve, reject) => {
                const req = store.add(record);
                req.onsuccess = () => resolve();
                req.onerror = reject;
            });
        }
        await new Promise<void>((resolve, reject) => {
            tx.oncomplete = () => resolve();
            tx.onerror = () => reject(tx.error);
        });
    } catch (error) {
        originalConsole.error('Logger: Failed to save to persistence:', error);
        // If saving fails, we might lose these logs, but don't block the app
    }
}

async function loadFromPersistence(limit: number): Promise<LogRecord[]> {
    const db = await getDb();
    if (!db) return [];

    try {
        const tx = db.transaction(defaultConfig.persistenceStoreName, 'readonly');
        const store = tx.objectStore(defaultConfig.persistenceStoreName);
        return new Promise<LogRecord[]>((resolve, reject) => {
            const request = store.getAll(null, limit); // Get oldest 'limit' records
            request.onsuccess = () => resolve(request.result || []);
            request.onerror = (event) => {
                originalConsole.error('Logger: Failed to load from persistence:', (event.target as IDBRequest).error);
                reject((event.target as IDBRequest).error);
                resolve([]); // Return empty on error
            };
        });
    } catch (error) {
        originalConsole.error('Logger: Error during persistence load transaction:', error);
        return [];
    }
}

// --- 同步日志处理函数 ---
function processLogSync(
    initialLevel: LogLevel, // Renamed from level for clarity
    context: string | null,
    args: any[],
    instanceFlowId?: string,
    logKind: LogRecord['logKind'] = 'generic',
    trackerEventData?: TrackerEventData,
): LogRecord | null {
    const config = { ...defaultConfig };
    if (context && contextConfigs.has(context)) {
        Object.assign(config, contextConfigs.get(context));
    }

    let currentLevel = initialLevel; // Start with the passed-in level

    // Adjust level for 'step' failures if it's a tracker event
    if (
        logKind === 'tracker' &&
        trackerEventData &&
        trackerEventData.type === 'step' &&
        trackerEventData.payload && // Ensure payload exists
        typeof trackerEventData.payload === 'object' && // Ensure payload is an object
        trackerEventData.payload.status === 'failure'
    ) {
        currentLevel = LogLevel.WARN;
    }

    // 1. Check Level (using currentLevel) and Enabled status
    if (currentLevel < config.level || currentLevel === LogLevel.OFF) {
        return null;
    }

    // 2. Create Log Record
    const timestamp = new Date().toISOString();
    const levelInfo = LogLevelInfo[currentLevel as keyof typeof LogLevelInfo]; // Use currentLevel
    let message = '';
    let errorInfo: LogRecord['error'] | undefined;
    const messageArgs: any[] = [];

    if (logKind === 'tracker' && trackerEventData) {
        message = `${trackerEventData.type} - ${trackerEventData.name}`;
        if (trackerEventData.status) {
            message += ` (${trackerEventData.status})`;
        }
        // For tracker events, args might be less relevant or could be in payload
    } else {
        // Process args for generic logs: find message string, Error object, and rest
        let errorArg: Error | undefined;
        for (const arg of args) {
            if (typeof arg === 'string' && !message) {
                message = arg;
            } else if (arg instanceof Error && !errorArg) {
                errorArg = arg;
                errorInfo = formatError(arg);
            } else {
                messageArgs.push(arg);
            }
        }
        if (!message && errorArg) {
            message = errorArg.message; // Use error message if no string message provided
        }
    }

    const record: LogRecord = {
        timestamp,
        level: currentLevel, // Use currentLevel
        levelName: levelInfo.name, // Derived from currentLevel
        message,
        args: messageArgs, // Store remaining args for generic logs
        context: context ?? undefined,
        sessionId: config.sessionId,
        globalContext: sanitizeForStorage(config.globalContext), // Ensure globalContext is sanitized
        error: errorInfo,
        flowId: instanceFlowId || (config as any).flowId,
        recordId: uuidv4(),
        synced: false,
        logKind,
        trackerEvent: trackerEventData ? (sanitizeForStorage(trackerEventData) as TrackerEventData) : undefined,
    };

    // 3. 同步控制台输出
    if (config.consoleOutput) {
        if (config.consoleJson) {
            originalConsole[levelInfo.method](JSON.stringify(record));
        } else {
            const simpleTimestamp = formatSimpleTimestamp();
            const prefix = `%c[${simpleTimestamp}][${record.levelName}]${record.context ? `[${record.context}]` : ''}`;
            const style = `color: ${levelInfo.color}; font-weight: bold;`;

            // 修改：根据日志类型构建不同的控制台参数
            let consoleArgs;
            if (logKind === 'tracker' && trackerEventData) {
                // 对于追踪事件，将trackerEventData添加到控制台输出
                consoleArgs = [prefix, style, record.message];

                // 添加追踪事件数据
                if (trackerEventData.payload) {
                    consoleArgs.push(trackerEventData.payload);
                }

                if (trackerEventData.error) {
                    consoleArgs.push(`Error: ${trackerEventData.error}`);
                }

                if (trackerEventData.durationMs) {
                    consoleArgs.push(`Duration: ${trackerEventData.durationMs}ms`);
                }
            } else {
                // 普通日志的原有逻辑
                consoleArgs = [prefix, style, record.message, ...record.args];
            }

            if (record.error?.stack && currentLevel >= LogLevel.ERROR) {
                consoleArgs.push('\n', record.error.stack);
            }
            originalConsole[levelInfo.method](...consoleArgs);
        }
    }

    return record;
}

// --- 处理日志的中间件和异步操作 ---
async function processLogAsync(record: LogRecord): Promise<void> {
    // 应用中间件
    if (middlewares.length > 0 && !isProcessingMiddleware) {
        try {
            isProcessingMiddleware = true;
            let currentRecord = record;
            for (const mw of middlewares) {
                const result = await mw(currentRecord);
                if (result === false || result === null) {
                    isProcessingMiddleware = false;
                    return; // 中间件跳过日志
                }
                currentRecord = result; // 中间件可能会转换记录
            }
            // 更新处理后的记录
            record = currentRecord;
        } catch (error) {
            originalConsole.error('Logger middleware error:', error);
        } finally {
            isProcessingMiddleware = false;
        }
    }

    // 添加到异步处理队列
    addToAsyncQueue(record);
}

// --- Logger Class ---
export class Logger {
    private context: string | null;
    private _flowId?: string;

    constructor(context?: string, flowId?: string) {
        this.context = context || null;
        this._flowId = flowId || '';
    }

    child(context: string): Logger {
        const childContext = this.context ? `${this.context}:${context}` : context;
        return new Logger(childContext, this._flowId); // 继承 _flowId
    }

    withFlowId(flowId: string): Logger {
        const childContext = this.context ? `${this.context}:flow-${flowId.substring(0, 8)}` : `flow-${flowId.substring(0, 8)}`;
        return new Logger(childContext, flowId);
    }

    // 同步日志方法 (trace, debug, info, warn, error, fatal)
    // These now pass 'generic' as logKind
    trace(...args: any[]): void {
        const record = processLogSync(LogLevel.TRACE, this.context, args, this._flowId, 'generic');
        if (record) {
            processLogAsync(record);
        }
    }

    debug(...args: any[]): void {
        const record = processLogSync(LogLevel.DEBUG, this.context, args, this._flowId, 'generic');
        if (record) {
            processLogAsync(record);
        }
    }

    info(...args: any[]): void {
        const record = processLogSync(LogLevel.INFO, this.context, args, this._flowId, 'generic');
        if (record) {
            processLogAsync(record);
        }
    }

    warn(...args: any[]): void {
        const record = processLogSync(LogLevel.WARN, this.context, args, this._flowId, 'generic');
        if (record) {
            processLogAsync(record);
        }
    }

    error(...args: any[]): void {
        const record = processLogSync(LogLevel.ERROR, this.context, args, this._flowId, 'generic');
        if (record) {
            processLogAsync(record);
        }
    }

    fatal(...args: any[]): void {
        const record = processLogSync(LogLevel.FATAL, this.context, args, this._flowId, 'generic');
        if (record) {
            processLogAsync(record);
        }
    }

    // 新增：专门用于记录追踪事件的方法
    track(level: LogLevel, eventData: TrackerEventData): void {
        // Tracker events might have a default level like INFO, or it can be specified.
        const record = processLogSync(level, this.context, [], this._flowId, 'tracker', eventData);
        if (record) {
            processLogAsync(record);
        }
    }

    // Performance timing
    time(label: string): void {
        timers.set(label, performance.now()); // Use performance.now() for higher precision
        this.debug(`Timer start: ${label}`);
    }

    timeEnd(label: string): void {
        const start = timers.get(label);
        if (start === undefined) {
            this.warn(`Timer end: No such label '${label}'`);
            return;
        }
        const duration = performance.now() - start;
        this.info(`Timer end: ${label}: ${duration.toFixed(2)}ms`);
        timers.delete(label);
    }

    // Console grouping (pass through to original console)
    group(label: string, ...args: any[]): void {
        originalConsole.group(label, ...args);
    }
    groupCollapsed(label: string, ...args: any[]): void {
        originalConsole.groupCollapsed(label, ...args);
    }
    groupEnd(): void {
        originalConsole.groupEnd();
    }

    // --- Static Configuration Methods ---

    static setConfig(cfg: Partial<LoggerConfig>) {
        Object.assign(defaultConfig, cfg);
        // Re-initialize session ID if provided
        if (cfg.sessionId) {
            defaultConfig.sessionId = cfg.sessionId;
        }
    }

    static setLevel(level: LogLevel) {
        defaultConfig.level = level;
    }

    static setSessionId(id: string) {
        defaultConfig.sessionId = id;
    }

    static setGlobalContext(context: Record<string, any>) {
        defaultConfig.globalContext = { ...defaultConfig.globalContext, ...context };
    }

    static updateGlobalContext(context: Record<string, any>) {
        Object.assign(defaultConfig.globalContext, context);
    }

    static setContextConfig(context: string, cfg: Partial<LoggerConfig>) {
        contextConfigs.set(context, cfg);
    }

    static clearContextConfig(context: string) {
        contextConfigs.delete(context);
    }

    // Middleware
    static use(mw: Middleware): void {
        middlewares.push(mw);
    }

    static clearMiddleware(): void {
        middlewares.length = 0;
    }

    // Cache/Persistence Control
    static getLocalStorageCache(): LogRecord[] {
        try {
            return JSON.parse(localStorage.getItem('LOGGER_CACHE') || '[]');
        } catch {
            return [];
        }
    }

    static clearLocalStorageCache(): void {
        localStorage.removeItem('LOGGER_CACHE');
    }

    static async getPersistenceCache(limit = 100): Promise<LogRecord[]> {
        return loadFromPersistence(limit);
    }

    static async clearPersistenceCache(): Promise<void> {
        const db = await getDb();
        if (!db) return;
        try {
            const tx = db.transaction(defaultConfig.persistenceStoreName, 'readwrite');
            const store = tx.objectStore(defaultConfig.persistenceStoreName);
            await new Promise<void>((resolve, reject) => {
                const req = store.clear();
                req.onsuccess = () => resolve();
                req.onerror = reject;
            });
            await new Promise<void>((resolve, reject) => {
                tx.oncomplete = () => resolve();
                tx.onerror = reject;
            });
            originalConsole.info('Logger: Persistence cache cleared.');
        } catch (error) {
            originalConsole.error('Logger: Failed to clear persistence cache:', error);
        }
    }

    // 更新日志同步状态
    static async updateLogSyncStatus(recordIds: string[], synced: boolean): Promise<void> {
        const db = await getDb();
        if (!db) return;

        try {
            const tx = db.transaction(defaultConfig.persistenceStoreName, 'readwrite');
            const store = tx.objectStore(defaultConfig.persistenceStoreName);
            const cursorReq = store.openCursor();

            await new Promise<void>((resolve, reject) => {
                const recordsToUpdate: { record: LogRecord; key: IDBValidKey }[] = [];
                cursorReq.onsuccess = (event) => {
                    const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result;
                    if (cursor) {
                        if (recordIds.includes(cursor.value.recordId)) {
                            recordsToUpdate.push({ record: cursor.value, key: cursor.primaryKey });
                        }
                        cursor.continue();
                    } else {
                        // All records processed
                        Promise.all(
                            recordsToUpdate.map((item) => {
                                item.record.synced = synced;
                                return new Promise<void>((updResolve, updReject) => {
                                    const putReq = store.put(item.record, item.key);
                                    putReq.onsuccess = () => updResolve();
                                    putReq.onerror = () => updReject(putReq.error);
                                });
                            }),
                        )
                            .then(() => {
                                tx.oncomplete = () => resolve();
                                tx.onerror = () => reject(tx.error);
                            })
                            .catch(reject);
                    }
                };
                cursorReq.onerror = (event) => {
                    reject((event.target as IDBRequest).error);
                };
            });
        } catch (error) {
            originalConsole.error('Logger: Failed to update sync status by recordIds:', error);
        }
    }

    // Global enable/disable (affects all loggers)
    static enable() {
        Logger.setLevel(import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.INFO);
    } // Restore default level

    static disable() {
        Logger.setLevel(LogLevel.OFF);
    } // Set level to OFF

    // 新增：提供对默认配置的只读访问
    static getDefaultConfig(): Readonly<LoggerConfig> {
        return { ...defaultConfig };
    }
}

// --- Console Interception ---
let isIntercepting = false;

export function interceptConsole() {
    if (isIntercepting) {
        globalLogger.warn('Console is already being intercepted.');
        return;
    }
    globalLogger.info('Intercepting native console methods...');
    console.log = (...args: any[]) => globalLogger.debug(...args);
    console.info = (...args: any[]) => globalLogger.info(...args);
    console.warn = (...args: any[]) => globalLogger.warn(...args);
    console.error = (...args: any[]) => globalLogger.error(...args);
    console.debug = (...args: any[]) => globalLogger.debug(...args);
    if (console.trace) {
        console.trace = (...args: any[]) => globalLogger.trace(...args);
    }
    // Keep original group/time methods
    console.group = originalConsole.group;
    console.groupCollapsed = originalConsole.groupCollapsed;
    console.groupEnd = originalConsole.groupEnd;
    console.time = (label?: string) => (label ? globalLogger.time(label) : originalConsole.time(label)); // Intercept if label provided
    console.timeEnd = (label?: string) => (label ? globalLogger.timeEnd(label) : originalConsole.timeEnd(label)); // Intercept if label provided

    isIntercepting = true;
}

export function restoreConsole() {
    if (!isIntercepting) {
        globalLogger.warn('Console is not currently intercepted.');
        return;
    }
    globalLogger.info('Restoring native console methods...');
    console.log = originalConsole.log;
    console.info = originalConsole.info;
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
    console.debug = originalConsole.debug;
    if (console.trace) {
        console.trace = originalConsole.trace;
    }
    console.group = originalConsole.group;
    console.groupCollapsed = originalConsole.groupCollapsed;
    console.groupEnd = originalConsole.groupEnd;
    console.time = originalConsole.time;
    console.timeEnd = originalConsole.timeEnd;

    isIntercepting = false;
}

// --- Global Instance & Exports ---
const globalLogger = new Logger('global'); // Default global logger with a context
export { globalLogger as logger };

// Attempt to save remaining logs on unload
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
        // No need to handle anything on unload since we're not doing remote sending
        originalConsole.info('Logger: Window unloading.');
    });
}
