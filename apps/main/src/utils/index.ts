export function parseURL(url: any) {
    const a = document.createElement('a');
    a.href = url;
    return {
        origin: a.origin,
        pathname: a.pathname,
        source: url,
        protocol: a.protocol.replace(':', ''),
        host: a.hostname,
        port: a.port,
        query: a.search,
        params: (function () {
            const params: any = {};
            const seg = a.search.replace(/^\?/, '').split('&');
            const len = seg.length;
            let p: string[] = [];
            for (let i = 0; i < len; i++) {
                if (seg[i]) {
                    p = seg[i]!.split('=');
                    params[p[0]!] = p[1];
                }
            }
            return params;
        })(),
        queryList: (function () {
            // 分割 pathname 并过滤掉空字符串，然后反转数组
            return a.pathname.split('/').filter(Boolean).reverse();
        })(),
        hash: a.hash.replace('#', ''),
        path: a.pathname.replace(/^([^/])/, '/$1'),
    };
}

function preloadImg(url: string) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
            resolve(url);
        };
        img.onerror = () => {
            reject(url);
        };

        img.src = url;
    });
}

export function imgPreload(list: Array<string>) {
    const promiseList: any[] = [];
    const imgList: Array<{ status: number; url: string }> = [];
    list.forEach((item) => {
        imgList.push({
            status: 0, // 0 未加载 1加载成功  2加载失败
            url: item,
        });
    });

    imgList.forEach((item) => {
        promiseList.push(preloadImg(item.url));
    });

    return Promise.all(promiseList);
}

export * from './logger';
export * from './send-error';
export * from './track-event';
export * from './number-to-chinese';
export * from './common';
export * from './qrcode-url';
