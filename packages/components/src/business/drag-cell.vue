<template>
    <div v-draggable :style="{ right: 0, zIndex: 999 }" class="drag-pannel">
        <div class="draggable-item">
            <slot />
        </div>
    </div>
</template>

<script lang="ts" setup>
const vDraggable = {
    mounted(el: HTMLElement) {
        let posY = (document.getElementById('app')?.scrollHeight || 0) - 120;

        el.style.top = `${posY}px`; // 初始化在右下角

        el.querySelector('.chat-entrance-trigger')!.addEventListener('mousedown', startDrag as EventListener); // 不是通用功能组件，这里直接写死了

        function startDrag(event: MouseEvent) {
            posY = el.offsetTop;
            const startY = event.clientY;

            function move(event: MouseEvent) {
                const dy = event.clientY - startY;
                const newPosY = posY + dy;
                if (newPosY >= 0 && newPosY + el.offsetHeight <= window.innerHeight) {
                    el.style.top = `${newPosY}px`;
                }
            }

            function stop() {
                document.removeEventListener('mousemove', move);
                document.removeEventListener('mouseup', stop);
            }

            document.addEventListener('mousemove', move);
            document.addEventListener('mouseup', stop);
        }
    },
    watch() {
        console.log('watch');
    },
};
</script>

<style lang="less" scoped>
.drag-pannel {
    position: fixed;
    z-index: 9999;
}
.draggable-item {
    cursor: ns-resize; /* 设置垂直方向的拖动光标 */
}
</style>
