# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
changes
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local
.vite
package-lock.json

# Editor directories and files
.VSCodeCounter
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.history

.turbo
.histoire

cache

*.tsbuildinfo
.temp

stats.html

*storybook.log

.mjs

auto-imports.d.ts
components.d.ts

.eslintcache
.stylelintcache

bundle-report.html

*.js.map
